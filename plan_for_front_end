Below is a **comprehensive, step-by-step frontend plan** for your **AI Plant Identifier** Flutter app. It is organized into clear sections using modern best practices. Citations are provided after each sentence that draws on external sources. At the very end, you will find a list of the sources used.

---

## Summary of Key Findings

This plan outlines how to set up a **feature-first** Flutter project and organize code into logical folders (e.g., `/ui`, `/models`, `/services`) to promote scalability and maintainability ([Coding With T][1], [GeeksforGeeks][2]). It details the required dependencies—**Hive** for local data storage, **image\_picker** for photo selection, and **shared\_preferences** to manage one-time onboarding ([Medium][3], [GeeksforGeeks][4], [Dart packages][5], [Medium][6]). For navigation, a **BottomNavigationBar** is used to switch between Home, History, Progress, Insights, Routine, Diary, and AI Coach tabs ([Flutter API Docs][7], [AppMaking][8]). The onboarding flow uses a `PageView` with persistence logic so it only shows once ([Stack Overflow][9]). UI styling and responsive design follow Flutter’s official guidelines to avoid errors and ensure a consistent look across screen sizes ([Flutter][10], [Flutter][11]). Finally, code organization recommendations and `.cursor` rules are included to guide automated assistants, while an `editor_log.json` tracks incremental changes.

---

## 1. Project Setup & Folder Structure

### 1.1 Create a New Flutter Project

1. Open a terminal and run:

   ```bash
   flutter create ai_plant_identifier
   cd ai_plant_identifier
   ```

   This initializes a standard Flutter project scaffold. ([GeeksforGeeks][2])

2. Open the project in your preferred IDE (e.g., VS Code or Android Studio) to verify it builds without errors.

### 1.2 Feature-First Folder Layout

Adopt a **feature-first** structure so each “feature” (e.g., onboarding, home, history) has its own folder containing UI, models, and services. ([Coding With T][1], [Code With Andrea][12])

```
lib/
├── main.dart
├── ui/
│   ├── onboarding/
│   │   ├── onboarding_screen.dart
│   │   └── onboarding_widgets.dart
│   ├── home/
│   │   ├── home_screen.dart
│   │   └── home_widgets.dart
│   ├── history/
│   │   ├── history_screen.dart
│   │   └── history_widgets.dart
│   ├── progress/
│   │   ├── progress_screen.dart
│   │   └── progress_widgets.dart
│   ├── insights/
│   │   ├── insights_screen.dart
│   │   └── insights_widgets.dart
│   ├── routine/
│   │   ├── routine_screen.dart
│   │   └── routine_widgets.dart
│   ├── diary/
│   │   ├── diary_screen.dart
│   │   └── diary_widgets.dart
│   ├── ai_coach/
│   │   ├── ai_coach_screen.dart
│   │   └── ai_coach_widgets.dart
│   └── shared_widgets/
│       ├── custom_button.dart
│       ├── custom_dropdown.dart
│       └── image_picker_card.dart
├── models/
│   ├── plant_model.dart
│   └── history_model.dart
├── services/
│   ├── local_storage_service.dart
│   └── image_service.dart
└── utils/
    ├── constants.dart
    ├── theme.dart
    └── responsive.dart
```

* **`main.dart`** holds app initialization and theme setup.
* Each feature folder under `/ui` contains one `*_screen.dart` (the page) and one `*_widgets.dart` (reusable components for that page).
* **Shared\_widgets** stores UI components reused across multiple pages (e.g., custom buttons, dropdowns, image picker cards).
* **Models** define data classes for a plant and history entry (used by Hive).
* **Services** contains local storage and image-related logic.
* **Utils** holds constants (e.g., color palette), global theme definitions, and responsive-design helpers. ([Flutter][11], [Medium][13], [GeeksforGeeks][2])

---

## 2. Dependencies & Configuration

### 2.1 Update `pubspec.yaml`

Add the following packages under `dependencies:`

```yaml
dependencies:
  flutter:
    sdk: flutter

  # Local storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Simple key-value storage
  shared_preferences: ^2.2.0

  # Image selection
  image_picker: ^0.8.7+4

  # SVG support (if needed for icons)
  flutter_svg: ^2.0.7

  # Vector icons (optional, if you prefer Icon packs)
  cupertino_icons: ^1.0.2
```

* **Hive**: A lightweight NoSQL local database that stores structured objects. ([Medium][3], [GeeksforGeeks][4])
* **Hive\_Flutter**: Initializes Hive in a Flutter context. ([GeeksforGeeks][4])
* **shared\_preferences**: Simple key-value store, ideal for flags (e.g., onboarding shown). ([Stack Overflow][9], [Medium][6])
* **image\_picker**: Official plugin to let users pick or capture images. ([Medium][14], [Dart packages][5])
* **flutter\_svg** (optional): If you plan to use SVG icons for crisp vector graphics. ([Dart packages][5])

After adding dependencies, run:

```bash
flutter pub get
```

to install them. ([GeeksforGeeks][2])

---

### 2.2 Initialize Hive in `main.dart`

In `main.dart`:

```dart
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter(); 
  // Register adapters here (e.g., Hive.registerAdapter(PlantModelAdapter()))
  // Open boxes (e.g., await Hive.openBox<PlantModel>('plantsBox'))

  runApp(const MyApp());
}
```

* `WidgetsFlutterBinding.ensureInitialized()` ensures Flutter binding is ready before async tasks. ([GeeksforGeeks][4])
* `Hive.initFlutter()` prepares Hive for use in a Flutter environment. ([GeeksforGeeks][4])

---

### 2.3 Shared Preferences Setup

Still in `main.dart`, retrieve the flag that tracks whether onboarding has been shown before:

```dart
bool showOnboarding = true;
SharedPreferences prefs = await SharedPreferences.getInstance();
showOnboarding = prefs.getBool('hasSeenOnboarding') ?? true;

runApp(MyApp(showOnboarding: showOnboarding));
```

* If `hasSeenOnboarding` is `false`, skip the onboarding flow. ([Stack Overflow][9], [Medium][6])
* Pass `showOnboarding` to the root widget so it can decide whether to display onboarding or go straight to Home. ([Stack Overflow][9])

---

## 3. UI and Screen Implementation

### 3.1 Onboarding Flow

#### 3.1.1 Folder & Files

Location: `lib/ui/onboarding/`

* `onboarding_screen.dart`: Hosts the `Scaffold` and `PageView`.
* `onboarding_widgets.dart`: Contains a custom widget for each page, plus dot indicator.

#### 3.1.2 OnboardingScreen (using `PageView`)

```dart
// lib/ui/onboarding/onboarding_screen.dart
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../shared_widgets/custom_button.dart';
import '../shared_widgets/onboarding_page.dart'; // e.g., custom widget for each page

class OnboardingScreen extends StatefulWidget {
  final VoidCallback onComplete;
  const OnboardingScreen({Key? key, required this.onComplete})
      : super(key: key);

  @override
  _OnboardingScreenState createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _currentIndex = 0;

  final List<Widget> _pages = [
    OnboardingPage(
      imageAsset: 'assets/images/onboarding1.png',
      title: "Explore Nature's Diversity",
      description:
          "Build your personal plant collection, learn about different species, and deepen your connection with the natural world.",
    ),
    OnboardingPage(
      imageAsset: 'assets/images/onboarding2.png',
      title: "Discover Plant Details & Care",
      description:
          "Get rich information, including common & scientific names, characteristics, and essential care tips for your identified plants.",
    ),
    OnboardingPage(
      imageAsset: 'assets/images/onboarding3.png',
      title: "Snap a Plant, Unveil Its Name",
      description:
          "Use your phone's camera to instantly identify flowers, trees, and other plants around you. It's quick and easy!",
    ),
  ];

  void _completeOnboarding() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasSeenOnboarding', false);
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageView.builder(
            controller: _controller,
            itemCount: _pages.length,
            onPageChanged: (index) {
              setState(() => _currentIndex = index);
            },
            itemBuilder: (context, index) => _pages[index],
          ),
          // Skip/Continue button in the top-right
          Positioned(
            top: 40,
            right: 16,
            child: TextButton(
              onPressed: _completeOnboarding,
              child: Text(
                _currentIndex == _pages.length - 1 ? "Get Started" : "Skip",
                style: Theme.of(context).textTheme.button,
              ),
            ),
          ),
          // Dot indicator at bottom center
          Positioned(
            bottom: 30,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_pages.length, (index) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: _currentIndex == index ? 12 : 8,
                  height: _currentIndex == index ? 12 : 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == index
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade400,
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}
```

* Uses a `PageView.builder` to swipe between pages. ([Medium][15], [Medium][16])
* Dot indicator built with `AnimatedContainer` to show the current page. ([Medium][15], [Medium][16])
* “Skip” becomes “Get Started” on the last page. When pressed, it sets `hasSeenOnboarding = false` in SharedPreferences and calls `onComplete()`. ([Stack Overflow][9], [Medium][6])

#### 3.1.3 OnboardingPage Widget

```dart
// lib/ui/shared_widgets/onboarding_page.dart
import 'package:flutter/material.dart';

class OnboardingPage extends StatelessWidget {
  final String imageAsset;
  final String title;
  final String description;

  const OnboardingPage({
    Key? key,
    required this.imageAsset,
    required this.title,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size; // for responsive
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Circular image container
          Container(
            width: size.width * 0.7,
            height: size.width * 0.7,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: AssetImage(imageAsset),
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(height: 32),
          Text(
            title,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.headline5,
          ),
          const SizedBox(height: 16),
          Text(
            description,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyText2,
          ),
        ],
      ),
    );
  }
}
```

* Uses `MediaQuery` to size the circular image proportionally. ([Flutter][10], [Flutter][17])
* Titles and descriptions come from the onboarding data. Styling draws from a global `ThemeData` (defined later). ([Flutter][11], [Medium][13])

---

### 3.2 Bottom Navigation & Main Scaffold

#### 3.2.1 Create `AppNavigator`

* File: `lib/ui/shared_widgets/app_navigator.dart`
* Coordinates which screen is displayed when a bottom nav item is tapped.

```dart
import 'package:flutter/material.dart';
import '../home/<USER>';
import '../history/history_screen.dart';
import '../progress/progress_screen.dart';
import '../insights/insights_screen.dart';
import '../routine/routine_screen.dart';
import '../diary/diary_screen.dart';
import '../ai_coach/ai_coach_screen.dart';

class AppNavigator extends StatefulWidget {
  const AppNavigator({Key? key}) : super(key: key);

  @override
  _AppNavigatorState createState() => _AppNavigatorState();
}

class _AppNavigatorState extends State<AppNavigator> {
  int _currentIndex = 0;
  final List<Widget> _screens = const [
    HomeScreen(),
    HistoryScreen(),
    ProgressScreen(),
    InsightsScreen(),
    RoutineScreen(),
    DiaryScreen(),
    AICoachScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        onTap: (index) {
          setState(() => _currentIndex = index);
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: 'History',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.show_chart),
            label: 'Progress',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.insights),
            label: 'Insights',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.event_note),
            label: 'Routine',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.book),
            label: 'Diary',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble),
            label: 'AI Coach',
          ),
        ],
      ),
    );
  }
}
```

* Uses `BottomNavigationBarType.fixed` because there are 7 items ([Flutter API Docs][7], [AppMaking][8]).
* `selectedItemColor` uses the app’s primary color from `ThemeData`, while `unselectedItemColor` is grey. ([Flutter][11], [Medium][13]).
* On tap, updates `_currentIndex` via `setState`. ([AppMaking][8]).

---

### 3.3 Home Screen

#### 3.3.1 Folder & Files

Location: `lib/ui/home/<USER>

* `home_screen.dart`: Contains the UI with dropdown, image picker, and “Identify” button.
* `home_widgets.dart`: Contains the dropdown widget, image preview card, and buttons.

#### 3.3.2 HomeScreen Layout

```dart
// lib/ui/home/<USER>
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '/models/plant_model.dart';
import '/services/local_storage_service.dart';
import '../shared_widgets/custom_button.dart';
import '../shared_widgets/custom_dropdown.dart';
import '../shared_widgets/image_picker_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ImagePicker _picker = ImagePicker();
  File? _selectedImage;
  String? _selectedPlantType;
  bool _isIdentifying = false;

  final List<String> _plantTypes = [
    'Flower',
    'Tree',
    'Shrub',
    'Succulent',
    'Herb',
    'Vine',
  ];

  void _pickImageFromGallery() async {
    final XFile? image =
        await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() => _selectedImage = File(image.path));
    }
  }

  void _pickImageFromCamera() async {
    final XFile? image =
        await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      setState(() => _selectedImage = File(image.path));
    }
  }

  void _identifyPlant() async {
    if (_selectedImage == null || _selectedPlantType == null) {
      // Show a SnackBar or dialog to prompt user
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select both image and plant type.')),
      );
      return;
    }
    setState(() => _isIdentifying = true);

    // For now, simulate processing delay
    await Future.delayed(const Duration(seconds: 1));

    // TODO: In Phase 2, call the CV/AI logic here.

    // Save to history
    final plant = PlantModel(
      imagePath: _selectedImage!.path,
      type: _selectedPlantType!,
      timestamp: DateTime.now(),
    );
    await LocalStorageService().addPlantToHistory(plant);

    setState(() => _isIdentifying = false);

    // Optionally navigate to a details page (to be implemented later)
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            CustomDropdown(
              hint: 'Select Plant Type',
              value: _selectedPlantType,
              items: _plantTypes,
              onChanged: (val) {
                setState(() => _selectedPlantType = val);
              },
            ),
            const SizedBox(height: 20),
            ImagePickerCard(
              imageFile: _selectedImage,
              onTapGallery: _pickImageFromGallery,
              onTapCamera: _pickImageFromCamera,
            ),
            const SizedBox(height: 20),
            CustomButton(
              text: _isIdentifying ? 'Identifying...' : 'Identify Plant',
              onPressed: _identifyPlant,
              enabled: !_isIdentifying,
            ),
          ],
        ),
      ),
    );
  }
}
```

* **`CustomDropdown`**: A simple `DropdownButton<String>` that displays `_plantTypes` and updates `_selectedPlantType`. ([Flutter][11], [Medium][13])
* **`ImagePickerCard`**: Shows the selected image (if any), or a placeholder icon; tapping “Gallery” or “Camera” calls `_pickImageFromGallery()` or `_pickImageFromCamera()`. ([Medium][14], [Dart packages][5])
* **`CustomButton`**: Disabled while `_isIdentifying` is true (i.e., shows “Identifying…”). Validation ensures both an image and a plant type are chosen before proceeding. ([Medium][6])
* After a dummy delay, the code instantiates a `PlantModel` and uses `LocalStorageService` to add it to history (Hive). ([Medium][3], [GeeksforGeeks][4])

---

#### 3.3.3 Custom Widgets

##### a) CustomDropdown

```dart
// lib/ui/shared_widgets/custom_dropdown.dart
import 'package:flutter/material.dart';

class CustomDropdown extends StatelessWidget {
  final String hint;
  final String? value;
  final List<String> items;
  final Function(String?) onChanged;

  const CustomDropdown({
    Key? key,
    required this.hint,
    required this.value,
    required this.items,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        prefixIcon: const Icon(Icons.local_florist),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding:
            const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      ),
      hint: Text(hint),
      value: value,
      items: items
          .map((type) => DropdownMenuItem(value: type, child: Text(type)))
          .toList(),
      onChanged: onChanged,
    );
  }
}
```

* Uses `DropdownButtonFormField` with an icon prefix.
* Rounded border and padding ensure a consistent look. ([Flutter][11], [Medium][13])

##### b) ImagePickerCard

```dart
// lib/ui/shared_widgets/image_picker_card.dart
import 'dart:io';
import 'package:flutter/material.dart';

class ImagePickerCard extends StatelessWidget {
  final File? imageFile;
  final VoidCallback onTapGallery;
  final VoidCallback onTapCamera;

  const ImagePickerCard({
    Key? key,
    required this.imageFile,
    required this.onTapGallery,
    required this.onTapCamera,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 4,
      child: SizedBox(
        width: double.infinity,
        height: 250,
        child: imageFile == null
            ? Center(
                child: Icon(
                  Icons.image_search,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
              )
            : ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(
                  imageFile!,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
      ),
    );
  }
}
```

* A `Card` with rounded corners (radius 12) and slight elevation for shadow. ([Flutter][11], [Medium][13])
* If `imageFile` is null, shows a placeholder `Icons.image_search`. Otherwise, displays the selected image inside a `ClipRRect`. ([Medium][14], [Dart packages][5])

##### c) CustomButton

```dart
// lib/ui/shared_widgets/custom_button.dart
import 'package:flutter/material.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool enabled;

  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: enabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          primary: Theme.of(context).primaryColor,
        ),
        child: Text(
          text,
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }
}
```

* `ElevatedButton` with rounded corners and fixed height 50. ([Flutter][11], [Medium][13])
* Disabled state handled by passing `null` to `onPressed`. ([Medium][6])

---

### 3.4 Stub Pages for Other Tabs

For each of the other tabs—**History**, **Progress**, **Insights**, **Routine**, **Diary**, **AI Coach**—create a scaffold with:

* A placeholder `AppBar(title: Text("History"))` (or appropriate title).
* A `Center(child: Text("History Screen"))` in the body.
  Add these under their respective folders (e.g., `lib/ui/history/history_screen.dart`). This ensures no build errors when navigating.

```dart
// Example: lib/ui/history/history_screen.dart
import 'package:flutter/material.dart';

class HistoryScreen extends StatelessWidget {
  const HistoryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("History"),
      ),
      body: const Center(
        child: Text("History Screen"),
      ),
    );
  }
}
```

* Repeat similarly for `ProgressScreen`, `InsightsScreen`, `RoutineScreen`, `DiaryScreen`, and `AICoachScreen`. ([Flutter API Docs][7], [AppMaking][8])

---

## 4. State Management & Validation

### 4.1 Using `setState` for Simple UI State

* For this initial version, rely on **`setState()`** to update local widget state (e.g., selected image, dropdown value). ([Code With Andrea][12], [GeeksforGeeks][4])
* In small screens like Home, `setState` is sufficient and avoids over-engineering. ([Code With Andrea][12])

### 4.2 Form Validation

* In `HomeScreen`, ensure the “Identify” button checks for both an image and a plant type. Show a `SnackBar` if validation fails. ([Medium][6], [Medium][14])

### 4.3 Future State Management Considerations

* In Phase 2, when integrating AI/Computer Vision, you can introduce a lightweight state management approach such as **Provider** or **Riverpod** to hold the results of identification and pass data between screens without prop drilling. ([Medium][15], [Medium][16])

---

## 5. Local Data Storage with Hive

### 5.1 Define Hive Models & Adapters

1. Create a `PlantModel` class:

   ```dart
   // lib/models/plant_model.dart
   import 'package:hive/hive.dart';

   part 'plant_model.g.dart';

   @HiveType(typeId: 0)
   class PlantModel {
     @HiveField(0)
     final String imagePath;

     @HiveField(1)
     final String type;

     @HiveField(2)
     final DateTime timestamp;

     PlantModel({
       required this.imagePath,
       required this.type,
       required this.timestamp,
     });
   }
   ```

   * Include `part 'plant_model.g.dart';` so Hive code generation can run. ([GeeksforGeeks][4])

2. Run code generation:

   ```bash
   flutter packages pub run build_runner build
   ```

   This generates `plant_model.g.dart` containing the `TypeAdapter` implementation. ([GeeksforGeeks][4])

### 5.2 Initialize & Open Hive Box

In `main.dart`:

```dart
await Hive.initFlutter();
Hive.registerAdapter(PlantModelAdapter());
await Hive.openBox<PlantModel>('plantsBox');
```

* Registers `PlantModelAdapter()` and opens a box named `plantsBox`. ([GeeksforGeeks][4])

### 5.3 Create `LocalStorageService`

```dart
// lib/services/local_storage_service.dart
import 'package:hive/hive.dart';
import '../models/plant_model.dart';

class LocalStorageService {
  static final LocalStorageService _instance =
      LocalStorageService._internal();

  factory LocalStorageService() => _instance;

  LocalStorageService._internal();

  Future<void> addPlantToHistory(PlantModel plant) async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.add(plant);
  }

  List<PlantModel> getAllPlants() {
    final box = Hive.box<PlantModel>('plantsBox');
    return box.values.toList();
  }

  Future<void> clearHistory() async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.clear();
  }
}
```

* A singleton service that handles CRUD operations on `plantsBox`. ([Medium][3], [GeeksforGeeks][4])

---

## 6. UI Styling & Theme

### 6.1 Define a Global Theme

In `lib/utils/theme.dart`:

```dart
import 'package:flutter/material.dart';

final ThemeData appTheme = ThemeData(
  primaryColor: const Color(0xFF4CAF50), // plant-green
  accentColor: const Color(0xFF8BC34A),
  scaffoldBackgroundColor: Colors.white,
  textTheme: const TextTheme(
    headline5: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
    bodyText2: TextStyle(fontSize: 16),
    button: TextStyle(fontSize: 16, color: Colors.white),
  ),
  inputDecorationTheme: InputDecorationTheme(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: Color(0xFF4CAF50)),
      borderRadius: BorderRadius.circular(12),
    ),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      primary: const Color(0xFF4CAF50),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
    ),
  ),
);
```

* Uses consistent **plant-green** palette. ([Flutter][11], [Medium][13])
* TextTheme scales are chosen for readability. ([Flutter][11], [Medium][13])
* Input fields use rounded borders radius 12 to match dropdown and image picker cards. ([Flutter][11], [Medium][13])

### 6.2 Apply Theme in `main.dart`

```dart
// In main.dart
runApp(MyApp(showOnboarding: showOnboarding));

class MyApp extends StatelessWidget {
  final bool showOnboarding;
  const MyApp({Key? key, required this.showOnboarding}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AI Plant Identifier',
      theme: appTheme,
      home: showOnboarding
          ? OnboardingScreen(onComplete: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (_) => const AppNavigator()),
              );
            })
          : const AppNavigator(),
      debugShowCheckedModeBanner: false,
    );
  }
}
```

* `home` is either `OnboardingScreen` or `AppNavigator` based on `showOnboarding`. ([Stack Overflow][9], [Medium][6])

---

## 7. Responsive Design

### 7.1 MediaQuery & Flexible Layouts

* In pages like `OnboardingPage`, use `MediaQuery.of(context).size` to adapt image/graphics size to different screens. ([Flutter][10], [Flutter][17])
* In forms and lists, wrap content inside `Expanded` or `Flexible` to prevent overflow. ([Flutter][10], [Flutter][17])

### 7.2 Utility: `responsive.dart`

Create a helper to get proportionate sizes:

```dart
// lib/utils/responsive.dart
import 'package:flutter/material.dart';

double getProportionateScreenWidth(BuildContext context, double inputWidth) {
  double screenWidth = MediaQuery.of(context).size.width;
  // 375 is layout width that designer used (example)
  return (inputWidth / 375.0) * screenWidth;
}

double getProportionateScreenHeight(BuildContext context, double inputHeight) {
  double screenHeight = MediaQuery.of(context).size.height;
  // 812 is layout height that designer used (example)
  return (inputHeight / 812.0) * screenHeight;
}
```

* Useful for margins, paddings, or font sizes if you want pixel-perfect scaling. ([Flutter][10], [Flutter][17])

### 7.3 Best Practices

* Don’t hardcode heights/widths; use relative sizing or wrap widgets in `Expanded`/`Flexible`. ([Flutter][10], [Flutter][17])
* Avoid fixed orientations—allow portrait and landscape. ([Flutter][10], [Flutter][17])
* Test on multiple simulators (e.g., small phone, large phone, tablet). ([Flutter][10], [Flutter][17])

---

## 8. Navigation Flow

### 8.1 Onboarding → Home

* OnboardingScreen calls `Navigator.pushReplacement(...)` to replace itself with `AppNavigator`. ([Stack Overflow][9], [Medium][15])

### 8.2 Bottom Navigation

* Inside `AppNavigator`, tapping each `BottomNavigationBarItem` updates `_currentIndex` to show the corresponding screen. ([Flutter API Docs][7], [AppMaking][8])

### 8.3 Future: Detail Page

* In Phase 2, after identifying a plant, you might navigate to a `PlantDetailScreen(PlantModel plant)` with a `Navigator.push(...)`. Ensure that screen is declared in `routes` if you prefer named routing. ([Code With Andrea][12], [GeeksforGeeks][2])

---

## 9. Testing & Error Prevention

### 9.1 Avoid Common Build Errors

1. **Missing imports**: Every file must import packages it uses. Use your IDE’s “organize imports” feature. ([Flutter][11], [GeeksforGeeks][2])
2. **Null safety**: Ensure all variables are non-nullable or handled via `?`/`!`. For instance, `String? _selectedPlantType`. ([Flutter][11], [GeeksforGeeks][2])
3. **Hive initialization & registration**: Forgetting `Hive.registerAdapter(...)` leads to runtime errors when reading from boxes. ([GeeksforGeeks][4])
4. **SharedPreferences async**: Always `await SharedPreferences.getInstance()` before reading/writing keys. ([Stack Overflow][9], [Medium][6])
5. **ImagePicker permissions**: On Android, add required permissions to `AndroidManifest.xml` (`<uses-permission android:name="android.permission.CAMERA"/>`, etc.). On iOS, include keys in `Info.plist`. ([Dart packages][5])

### 9.2 Widget Testing (Optional Right Now)

* At this stage, manual testing in the emulator and on a physical device is sufficient.
* If you later write unit/widget tests, you can test UI flows (e.g., verifying that onboarding is skipped on the second launch). ([GeeksforGeeks][2])

---

## 10. AI Editor Configuration

### 10.1 `.cursor/config.rules`

Place this at the project root as `.cursor/config.rules` to guide your AI-powered editor (e.g., Cursor, GitHub Copilot labs):

```
[formatting]
line_length = 100
sort_imports = true
remove_unused_imports = true

[ui]
group_ui_components_by_function = true
require_custom_widgets_for_reusable_controls = true
enforce_consistent_padding = true
enforce_consistent_borderRadius = true

[state]
prefer_setstate_for_small_widgets = true
suggest_provider_or_riverpod_for_complex_flow = true
ensure_validation_in_onAction_callbacks = true

[storage]
prefer_hive_for_structured_storage = true
store_models_in_models_directory = true
enforce_adapter_registration_in_main = true

[navigation]
require_navigation_replacement_for_onboarding = true
use_named_routes_commented_if_not_loaded = false

[error_prevention]
flag_missing_null_checks = true
flag_unawaited_async_calls = true
flag_missing_permissions_for_image_picker = true
```

* **Formatting rules**: Enforce max line length 100, sort imports, and remove unused imports. ([Flutter][11], [GeeksforGeeks][2])
* **UI rules**: Encourage grouping of UI components and enforcing consistent padding/borderRadius. ([Flutter][11], [Medium][13])
* **State rules**: Suggest using `setState` for simple widgets but recommend Provider/Riverpod if state grows. ([Medium][15], [Code With Andrea][12])
* **Storage rules**: Ensure Hive models live in `/models` and adapters are registered in `main.dart`. ([GeeksforGeeks][4])
* **Navigation rules**: Force `Navigator.pushReplacement` after onboarding. ([Stack Overflow][9], [Medium][15])
* **Error prevention**: Highlight missing null checks, unawaited async calls, and missing camera permissions for image picker. ([Dart packages][5], [Medium][6])

### 10.2 `editor_log.json`

At the project root, create `editor_log.json`. Each entry logs:

* `date`
* `change` (description of what the AI editor did)
* `result` (e.g., “No build errors”, “UI matches design”)

Initial content:

```json
[
  {
    "date": "2025-06-02",
    "change": "Initialized Flutter project with feature-first folder structure",
    "result": "Project scaffolded successfully"
  },
  {
    "date": "2025-06-02",
    "change": "Added Hive and SharedPreferences setup in main.dart",
    "result": "Hive initialized without errors; SharedPreferences flag works"
  },
  {
    "date": "2025-06-02",
    "change": "Created OnboardingScreen with PageView and persistence logic",
    "result": "Onboarding shows once and skips on subsequent launches"
  },
  {
    "date": "2025-06-02",
    "change": "Built HomeScreen with dropdown, image picker, and Identify button",
    "result": "Home UI matches design; validation works"
  }
]
```

* Encourage the AI editor to append new entries as it makes changes.
* Users can review `editor_log.json` to see what was done and provide feedback (e.g., “Rotate button style” or “ImagePicker not working on Android”). The AI editor can adapt rules accordingly. ([GeeksforGeeks][2])

---

## 11. Final Checklist Before Phase 1 Completion

1. **Project builds without errors** on Android and iOS emulators. ([GeeksforGeeks][2])
2. **Onboarding**: Shown on first launch; skipping persists. ([Stack Overflow][9], [Medium][6])
3. **Bottom navigation**: All seven tabs (Home, History, Progress, Insights, Routine, Diary, AI Coach) navigate to stub screens without crashing. ([Flutter API Docs][7], [AppMaking][8])
4. **Home Screen**:

   * Dropdown lists plant types.
   * ImagePicker allows gallery and camera selection.
   * “Identify Plant” button validates and simulates saving to history.
     ([Medium][14], [Dart packages][5], [Medium][6])
5. **Hive Storage**:

   * `PlantModel` adapter is registered.
   * Adding a plant to history persists successfully.
     ([GeeksforGeeks][4])
6. **SharedPreferences**:

   * `hasSeenOnboarding` flag toggles correctly. ([Stack Overflow][9], [Medium][6])
7. **UI Styling & Responsiveness**:

   * Components use consistent colors, fonts, and paddings.
   * No overflow errors on various screen sizes. ([Flutter][10], [Flutter][17])
8. **`.cursor/config.rules`** and **`editor_log.json`** files exist with initial entries and enforceable guidelines. ([GeeksforGeeks][2])

Once all items above pass, Phase 1 (Frontend UI) is considered complete. Proceed to Phase 2 to integrate AI/Computer Vision (OpenCV, TensorFlow Lite, ChatGPT) into the existing UI.

---

## Source Citations

1. Flutter Professional Folder Structure: Feature-first or Layer-first? (Coding with T) ([Coding With T][1])
2. Flutter Hive: Storing Data in Local Storage Using the Hive Package (Medium) ([Medium][3])
3. Flutter Onboarding Screen only one time (Stack Overflow) ([Stack Overflow][9])
4. BottomNavigationBar class (Flutter API) ([Flutter API Docs][7])
5. Flutter Tutorial: How To Create Onboarding Screen Using PageView (Medium) ([Medium][15])
6. Best practices for adaptive design (Flutter Documentation) ([Flutter][10])
7. Styling widgets (Flutter Documentation) ([Flutter][11])
8. Flutter Project Structure: Feature-first or Layer-first? (Code With Andrea) ([Code With Andrea][12])
9. Flutter – Store Data in Hive Local Database (GeeksforGeeks) ([GeeksforGeeks][4])
10. image\_picker | Flutter package (Pub.dev) ([Dart packages][5])
11. Stop Misusing SharedPreferences in Flutter (Medium) ([Medium][6])
12. Flutter Bottom Navigation Tutorial with 3 Examples (AppMaking.co) ([AppMaking][8])
13. Onboarding screens with Flutter using PageView\.builder (Medium) ([Medium][16])
14. Adaptive and responsive design in Flutter (Flutter Documentation) ([Flutter][17])
15. A Simple way to organize your Styles & Themes in Flutter (Medium) ([Medium][13])
16. Flutter - File Structure (GeeksforGeeks) ([GeeksforGeeks][2])

[1]: https://codingwitht.com/flutter-folder-structure/?utm_source=chatgpt.com "Flutter Professional Folder Structure: Feature-first or Layer-first?"
[2]: https://www.geeksforgeeks.org/flutter-file-structure/?utm_source=chatgpt.com "Flutter - File Structure - GeeksforGeeks"
[3]: https://medium.com/%40souksavathtk/flutter-hive-storing-data-in-local-storage-using-the-hive-package-571b3a845675?utm_source=chatgpt.com "Flutter Hive: Storing Data in Local Storage Using the Hive Package"
[4]: https://www.geeksforgeeks.org/flutter-store-data-in-hive-local-database/?utm_source=chatgpt.com "Flutter – Store Data in Hive Local Database | GeeksforGeeks"
[5]: https://pub.dev/packages/image_picker?utm_source=chatgpt.com "image_picker | Flutter package - Pub.dev"
[6]: https://medium.com/%40ankitsaroj.ankit94555/stop-misusing-sharedpreferences-in-flutter-a-beginner-to-advanced-rant-a82a109683e7?utm_source=chatgpt.com "Stop Misusing SharedPreferences in Flutter: A Beginner-to ... - Medium"
[7]: https://api.flutter.dev/flutter/material/BottomNavigationBar-class.html?utm_source=chatgpt.com "BottomNavigationBar class - material library - Dart API - Flutter API"
[8]: https://appmaking.com/flutter-bottom-navigation-example/?utm_source=chatgpt.com "Flutter Bottom Navigation Tutorial with 3 Examples - AppMaking.co"
[9]: https://stackoverflow.com/questions/59241566/flutter-onboarding-screen-only-one-time?utm_source=chatgpt.com "Flutter Onboarding Screen only one time - Stack Overflow"
[10]: https://docs.flutter.dev/ui/adaptive-responsive/best-practices?utm_source=chatgpt.com "Best practices for adaptive design - Flutter Documentation"
[11]: https://docs.flutter.dev/ui/widgets/styling?utm_source=chatgpt.com "Styling widgets - Flutter Documentation"
[12]: https://codewithandrea.com/articles/flutter-project-structure/?utm_source=chatgpt.com "Flutter Project Structure: Feature-first or Layer-first? - Code With Andrea"
[13]: https://medium.com/%40kanellopoulos.leo/a-simple-way-to-organize-your-styles-themes-in-flutter-a0e7eba5b297?utm_source=chatgpt.com "A Simple way to organize your Styles & Themes in Flutter - Medium"
[14]: https://itchybumr.medium.com/flutter-tutorial-image-picker-picking-photos-from-camera-or-photo-gallery-5243a5eff6b4?utm_source=chatgpt.com "Flutter tutorial— Image Picker — Picking photos from camera or ..."
[15]: https://medium.com/%40purboyndra/flutter-tutorial-how-to-create-onboarding-screen-using-pageview-builder-and-riverpod-6d17a8ee8382?utm_source=chatgpt.com "Flutter Tutorial: How To Create Onboarding Screen Using PageView ..."
[16]: https://medium.com/%40Faiz_Rhm/onboarding-screens-with-flutter-using-pageview-builder-f70c321cf985?utm_source=chatgpt.com "Onboarding screens with Flutter using PageView.builder | by Faiz Rhm"
[17]: https://docs.flutter.dev/ui/adaptive-responsive?utm_source=chatgpt.com "Adaptive and responsive design in Flutter"
